name: Test & Build

on:
    push:
        branches:
            - dev
    pull_request:
        types: [review_requested]
        branches:
            - dev

jobs:
    test:
        runs-on: ubuntu-24.04
        steps:
            - name: Checkout repo
              uses: actions/checkout@v5

            - name: Setup Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: '20.x'
                  cache: 'yarn'

            - name: Install dependencies
              run: yarn install --frozen-lockfile

            - name: Lint
              run: yarn lint

            - name: Run tests
              run: yarn test:types

    trigger:
        needs: test
        if: github.event_name != 'pull_request'
        runs-on: ubuntu-24.04
        steps:
            - name: Trigger Cloudflare Hook
              run: curl -X POST "${{ secrets.CLOUDFLARE_DEPLOY_HOOK }}"
