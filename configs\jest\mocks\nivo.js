// Mock for @nivo charts
module.exports = {
    ResponsivePieCanvas: ({ data, ...props }) => {
        return {
            $$typeof: Symbol.for('react.element'),
            type: 'div',
            props: {
                'data-testid': 'mock-pie-chart',
                'data-chart-data': JSON.stringify(data),
                ...props
            }
        };
    },
    ResponsivePie: ({ data, ...props }) => {
        return {
            $$typeof: Symbol.for('react.element'),
            type: 'div',
            props: {
                'data-testid': 'mock-pie-chart',
                'data-chart-data': JSON.stringify(data),
                ...props
            }
        };
    },
    ResponsiveBar: ({ data, ...props }) => {
        return {
            $$typeof: Symbol.for('react.element'),
            type: 'div',
            props: {
                'data-testid': 'mock-bar-chart',
                'data-chart-data': JSON.stringify(data),
                ...props
            }
        };
    },
    ResponsiveLine: ({ data, ...props }) => {
        return {
            $$typeof: Symbol.for('react.element'),
            type: 'div',
            props: {
                'data-testid': 'mock-line-chart',
                'data-chart-data': JSON.stringify(data),
                ...props
            }
        };
    }
};
