import { ColumnActionText } from '@common';
import { routeConstants } from '@constants';
import { TableColumnConfig, Text } from '@medly-components/core';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '@slice';

export const SelfReviewActionFormatter: TableColumnConfig['component'] = ({ rowData }) => {
    const navigateTo = useNavigate();
    const userDetails = useAppSelector(state => state.user);
    const isReviewCycleActive = rowData?.isReviewCycleActive;

    const actionToShow = useMemo(() => {
        // Only show actions when review cycle is active or when viewing published reviews
        if (!isReviewCycleActive && !rowData?.publish) {
            return 'N/A';
        }

        if (rowData?.publish) return 'View';

        if (!isReviewCycleActive) return 'N/A';

        // If self review period has passed and nothing is published, show N/A
        if (rowData?.isSelfReviewDatePassed && !rowData?.publish) {
            return 'N/A';
        }

        // If there's a draft (auto-save happened at least once), show Edit
        if (rowData?.draft) return 'Edit';

        // If self review is active or not started, show Add
        if (rowData?.isSelfReviewActive || (!rowData?.isSelfReviewActive && !rowData?.isSelfReviewDatePassed)) {
            return 'Add';
        }

        return 'N/A';
    }, [rowData, isReviewCycleActive]);

    const handleClick = () => {
        navigateTo(
            `${
                actionToShow !== 'View'
                    ? routeConstants.selfReviewPerformanceGuidelines
                    : `/performance-review/self-review/${rowData?.reviewCycle}/review-summary`
            }`,
            {
                state: {
                    ...rowData,
                    reviewFromId: userDetails.id,
                    reviewToId: userDetails.id,
                    action: actionToShow
                }
            }
        );
    };

    return actionToShow !== 'N/A' ? <ColumnActionText onClick={handleClick}>{actionToShow}</ColumnActionText> : <Text>N/A</Text>;
};
