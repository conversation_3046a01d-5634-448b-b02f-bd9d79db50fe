import styled from 'styled-components';

export const GridContainer = styled.div`
    &.row {
        display: flex;
        flex-flow: row wrap;
        max-width: 75rem;
        margin-right: 0;
        margin-left: 0;
    }

    &.row.expanded {
        max-width: none;
    }

    &.row.column-gap-1 {
        column-gap: 1rem;
    }
    &.row.column-gap-2 {
        column-gap: 2rem;
    }
    &.row.column-gap-3 {
        column-gap: 3rem;
    }

    &.flex-start {
        justify-content: flex-start;
    }

    &.center {
        justify-content: center;
    }

    &.flex-end {
        justify-content: flex-end;
    }

    &.space-between {
        justify-content: space-between;
    }

    &.space-around {
        justify-content: space-around;
    }

    &.space-evenly {
        justify-content: space-evenly;
    }

    &.align-center {
        align-items: center;
    }

    &.align-flex-start {
        align-items: flex-start;
    }

    &.align-flex-end {
        align-items: flex-end;
    }

    &.column {
        display: flex;
        flex: 1 1 0;
        padding-left: 0.9375rem;
        padding-right: 0.9375rem;
        box-sizing: border-box;
    }

    &.column-direction {
        flex-direction: column;
    }

    &.column > .row {
        margin-left: -0.9375rem;
        margin-right: -0.9375rem;
    }

    &.sm-1 {
        flex: 0 0 8.33333%;
        max-width: 8.33333%;
    }

    &.sm-2 {
        flex: 0 0 16.66667%;
        max-width: 16.66667%;
    }

    &.sm-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }

    &.sm-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%;
    }

    &.sm-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%;
    }

    &.sm-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    &.sm-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%;
    }

    &.sm-8 {
        flex: 0 0 66.66667%;
        max-width: 66.66667%;
    }

    &.sm-9 {
        flex: 0 0 75%;
        max-width: 75%;
    }

    &.sm-10 {
        flex: 0 0 83.33333%;
        max-width: 83.33333%;
    }

    &.sm-11 {
        flex: 0 0 91.66667%;
        max-width: 91.66667%;
    }

    &.sm-12 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    &.margin-1 {
        margin: 1rem;
    }
    &.margin-2 {
        margin: 2rem;
    }
    &.margin-3 {
        margin: 3rem;
    }
    &.marginLeft-1 {
        margin-left: 1rem;
    }
    &.marginLeft-2 {
        margin-left: 2rem;
    }
    &.marginLeft-3 {
        margin-left: 3rem;
    }
    &.marginRight-1 {
        margin-right: 1rem;
    }
    &.marginRight-2 {
        margin-right: 2rem;
    }
    &.marginRight-3 {
        margin-right: 3rem;
    }
    &.marginTop-1 {
        margin-top: 1rem;
    }
    &.marginTop-2 {
        margin-top: 2rem;
    }
    &.marginTop-3 {
        margin-top: 3rem;
    }
    &.marginBottom-1 {
        margin-bottom: 1rem;
    }
    &.marginBottom-2 {
        margin-bottom: 2rem;
    }
    &.marginBottom-3 {
        margin-bottom: 3rem;
    }

    @media screen and (min-width: 40em) {
        &.md-1 {
            flex: 0 0 8.33333%;
            max-width: 8.33333%;
        }

        &.md-2 {
            flex: 0 0 16.66667%;
            max-width: 16.66667%;
        }

        &.md-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        &.md-4 {
            flex: 0 0 33.33333%;
            max-width: 33.33333%;
        }

        &.md-5 {
            flex: 0 0 41.66667%;
            max-width: 41.66667%;
        }

        &.md-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        &.md-7 {
            flex: 0 0 58.33333%;
            max-width: 58.33333%;
        }

        &.md-8 {
            flex: 0 0 66.66667%;
            max-width: 66.66667%;
        }

        &.md-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }

        &.md-10 {
            flex: 0 0 83.33333%;
            max-width: 83.33333%;
        }

        &.md-11 {
            flex: 0 0 91.66667%;
            max-width: 91.66667%;
        }

        &.md-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    @media screen and (min-width: 64em) {
        &.lg-1 {
            flex: 0 0 8.33333%;
            max-width: 8.33333%;
        }

        &.lg-2 {
            flex: 0 0 16.66667%;
            max-width: 16.66667%;
        }

        &.lg-3 {
            flex: 0 0 25%;
            max-width: 25%;
        }

        &.lg-4 {
            flex: 0 0 33.33333%;
            max-width: 33.33333%;
        }

        &.lg-5 {
            flex: 0 0 41.66667%;
            max-width: 41.66667%;
        }

        &.lg-6 {
            flex: 0 0 50%;
            max-width: 50%;
        }

        &.lg-7 {
            flex: 0 0 58.33333%;
            max-width: 58.33333%;
        }

        &.lg-8 {
            flex: 0 0 66.66667%;
            max-width: 66.66667%;
        }

        &.lg-9 {
            flex: 0 0 75%;
            max-width: 75%;
        }

        &.lg-10 {
            flex: 0 0 83.33333%;
            max-width: 83.33333%;
        }

        &.lg-11 {
            flex: 0 0 91.66667%;
            max-width: 91.66667%;
        }

        &.lg-12 {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }
`;
