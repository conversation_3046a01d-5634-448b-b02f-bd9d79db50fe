import { Theme } from '@medly-components/theme';
import { InjectClassName, WithThemeProp } from '@medly-components/utils';
import styled, { css } from 'styled-components';
import { TextFieldProps } from '@medly-components/core';

type IconProps = Pick<Required<TextFieldProps>, 'size' | 'variant'> & {
    disabled?: boolean;
    isActive?: boolean;
    isErrorPresent?: boolean;
};

const getStyleForIcon = ({ theme, variant, disabled }: IconProps & WithThemeProp, state: 'default' | 'active' | 'error' | 'disabled') => {
    const variantTheme = theme.textField[variant];
    return css`
        * {
            fill: ${variantTheme[state].borderColor};
        }
        &:hover {
            * {
                fill: ${!disabled && variantTheme.hover.borderColor};
            }
        }
    `;
};

const disableCursor = css`
    &&& {
        cursor: not-allowed;
    }
`;

export const IconWrapper = styled(InjectClassName)<IconProps & WithThemeProp>`
    pointer-events: none;
    ${props => getStyleForIcon(props as IconProps & { theme: Theme }, 'default')};
    ${props => props.isActive && getStyleForIcon(props as IconProps & { theme: Theme }, 'active')};
    ${props => props.isErrorPresent && getStyleForIcon(props as IconProps & { theme: Theme }, 'error')};
    ${props => props.disabled && getStyleForIcon(props as IconProps & { theme: Theme }, 'disabled')};
    ${props => props.disabled && disableCursor};
`;

export const Prefix = styled(IconWrapper)<IconProps>`
    margin-right: ${({ size }) => (size === 'S' ? '1.2rem' : '1.6rem')};
`;

export const Suffix = styled(IconWrapper)<IconProps>`
    margin-left: ${({ size }) => (size === 'S' ? '1.2rem' : '1.6rem')};
`;
