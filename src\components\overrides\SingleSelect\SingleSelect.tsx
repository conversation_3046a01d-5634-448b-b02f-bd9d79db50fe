import { ChevronDownIcon } from '@medly-components/icons';
import { useCombinedRefs, useOuterClickNotifier, useUpdateEffect, WithStyle } from '@medly-components/utils';
import type { FC } from 'react';
import { forwardRef, memo, useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { TextField } from '@medly-components/core';
import FlatVariant from './FlatVariant';
import { filterOptions, getDefaultSelectedOption, getUpdatedOptions } from './helpers';
import Options from './Options';
import * as Styled from './SingleSelect.styled';
import { Option, SingleSelectProps } from './types';
import { useKeyboardNavigation } from './useKeyboardNavigation';

const Component: FC<SingleSelectProps> = memo(
    forwardRef((props, ref) => {
        const {
                id,
                value,
                onChange,
                options: defaultOptions,
                variant,
                minWidth,
                maxWidth,
                includesNestedOptions,
                fullWidth,
                disabled,
                showDecorators,
                onFocus,
                onBlur,
                className,
                validator,
                isSearchable,
                suffix,
                isUnselectable,
                optionsPosition,
                ...inputProps
            } = props,
            selectId = useMemo(() => id || inputProps.label?.toLocaleLowerCase() || 'medly-singleSelect', [id, inputProps.label]),
            defaultSelectedOption = getDefaultSelectedOption(defaultOptions, value);

        const wrapperRef = useRef<HTMLDivElement>(null),
            optionsRef = useRef<HTMLUListElement>(null),
            inputRef = useCombinedRefs<HTMLInputElement>(ref, useRef(null)),
            isFocused = useRef(false),
            [minHeight, setMinHeight] = useState(''),
            [areOptionsVisible, setOptionsVisibilityState] = useState(false),
            [inputValue, setInputValue] = useState(defaultSelectedOption.label),
            [selectedOption, setSelectedOption] = useState(defaultSelectedOption),
            [builtInErrorMessage, setErrorMessage] = useState(''),
            [options, setOptions] = useState(getUpdatedOptions(defaultOptions, defaultSelectedOption));

        const updateToDefaultOptions = useCallback(
            () => setOptions(getUpdatedOptions(defaultOptions, selectedOption)),
            [defaultOptions, selectedOption]
        );

        const validate = useCallback(
            () =>
                setErrorMessage(
                    validator?.(inputRef.current?.value || value) || (inputProps?.required && !value && 'Please select one option.') || ''
                ),
            [inputProps?.required, validator, value]
        );

        const showOptions = useCallback(() => {
                setOptionsVisibilityState(true);
                isSearchable && setInputValue('');
                inputRef.current?.focus();
            }, [isSearchable, inputValue]),
            hideOptions = useCallback((selected?: Option) => {
                setOptionsVisibilityState(false);
                inputRef.current?.blur();
                selected && setInputValue(selected.label);
            }, []),
            toggleOptions = useCallback(() => {
                const selected = getDefaultSelectedOption(defaultOptions, value);
                return !disabled && (areOptionsVisible ? hideOptions(selected) : showOptions());
            }, [disabled, areOptionsVisible, defaultOptions, value]),
            handleInputChange = useCallback(
                (event: React.ChangeEvent<HTMLInputElement>) => {
                    const { value: inputValue } = event.target as HTMLInputElement,
                        newOptions = filterOptions(getUpdatedOptions(defaultOptions, selectedOption), inputValue);
                    setInputValue(inputValue);
                    newOptions.length && inputValue ? setOptions(newOptions) : updateToDefaultOptions();
                    if (newOptions.length === 1 && newOptions[0].label === inputValue) {
                        !areOptionsVisible && onChange && onChange(newOptions[0].value);
                    } else if (!areOptionsVisible) {
                        setOptionsVisibilityState(true);
                    }
                },
                [areOptionsVisible, defaultOptions, selectedOption, updateToDefaultOptions]
            ),
            handleOptionClick = useCallback(
                (option: Option) => {
                    if (!option.disabled && !Array.isArray(option.value)) {
                        setSelectedOption(option);
                        setOptions(getUpdatedOptions(options, option));
                        hideOptions(option);
                        isUnselectable && value === option.value ? onChange?.('') : onChange?.(option.value);
                        setErrorMessage('');
                    } else {
                        inputRef.current?.focus();
                    }
                },
                [inputRef.current, isUnselectable, value, options, onChange]
            ),
            handleOuterClick = useCallback(() => {
                isFocused.current = false;
                if (areOptionsVisible) {
                    hideOptions(defaultSelectedOption);
                    validate();
                    updateToDefaultOptions();
                }
            }, [areOptionsVisible, selectedOption, updateToDefaultOptions, validate]),
            handleFocus = useCallback(
                (event: React.FocusEvent<HTMLInputElement>) => {
                    isFocused.current = true;
                    onFocus?.(event);
                },
                [onFocus]
            ),
            handleBlur = useCallback(
                (event: React.FocusEvent<HTMLInputElement>) => {
                    isFocused.current = false;
                    onBlur?.(event);
                },
                [onBlur]
            ),
            inputValidator = useCallback(() => '', []),
            handleKeyPress = useCallback((event: React.KeyboardEvent) => !isSearchable && event.preventDefault(), [isSearchable]);

        useUpdateEffect(() => {
            const selected = getDefaultSelectedOption(defaultOptions, value);
            setInputValue(selected.label);
            setSelectedOption(selected);
            setOptions(getUpdatedOptions(defaultOptions, selected));
            value && value !== selected.value && onChange && onChange(selected.value);
        }, [defaultOptions, value]);

        useKeyboardNavigation({
            isFocused,
            selectedOption,
            options,
            areOptionsVisible,
            setOptions,
            handleOptionClick,
            showOptions,
            optionsRef,
            hideOptions
        });

        useOuterClickNotifier(() => {
            handleOuterClick();
        }, wrapperRef);

        const commonProps = {
            id: `${selectId}`,
            ref: inputRef,
            value: inputValue,
            label: inputProps.label,
            helperText: inputProps.helperText,
            errorText: inputProps.errorText || builtInErrorMessage,
            onFocus: handleFocus,
            onBlur: handleBlur,
            onKeyPress: handleKeyPress,
            disabled,
            showDecorators,
            areOptionsVisible,
            onInvalid: validate,
            validator: inputValidator
        };

        useLayoutEffect(() => {
            setTimeout(
                () =>
                    optionsPosition === 'relative' &&
                    setMinHeight(
                        areOptionsVisible ? `${(wrapperRef.current?.clientHeight || 0) + (optionsRef.current?.clientHeight || 0)}px` : ''
                    ),
                0
            );
        }, [areOptionsVisible, optionsPosition]);

        return (
            <Styled.Wrapper
                id={`${selectId}-wrapper`}
                {...{ disabled, maxWidth, fullWidth }}
                minWidth={variant === 'flat' ? 'fit-content' : minWidth}
                variant={variant!}
                ref={wrapperRef}
                className={className}
                isSearchable={isSearchable}
                isErrorPresent={!!props.errorText}
                onClick={toggleOptions}
                areOptionsVisible={areOptionsVisible}
                minHeight={minHeight}
            >
                {variant === 'flat' ? (
                    <FlatVariant {...commonProps} />
                ) : (
                    <TextField
                        fullWidth
                        variant={variant}
                        autoComplete="off"
                        onChange={handleInputChange}
                        suffix={suffix || ChevronDownIcon}
                        {...inputProps}
                        {...commonProps}
                        minWidth={minWidth}
                        maxWidth={maxWidth}
                        inputMode={!isSearchable ? 'none' : inputProps?.inputMode}
                    />
                )}
                {!disabled && areOptionsVisible && (
                    <Options
                        size={variant === 'flat' ? 'S' : inputProps.size!}
                        ref={optionsRef}
                        variant={variant!}
                        id={`${selectId}-options`}
                        options={options}
                        hasError={!!props.errorText}
                        onOptionClick={handleOptionClick}
                        maxWidth={maxWidth}
                        includesNestedOptions={includesNestedOptions}
                    />
                )}
            </Styled.Wrapper>
        );
    })
);

Component.displayName = 'SingleSelect';
Component.defaultProps = {
    value: '',
    size: 'M',
    label: '',
    minWidth: '20rem',
    variant: 'filled',
    fullWidth: false,
    required: false,
    isSearchable: false,
    includesNestedOptions: false,
    placeholder: 'Please Select . . .',
    showDecorators: true,
    isUnselectable: false
};
export const SingleSelect: FC<SingleSelectProps> & WithStyle = Object.assign(Component, { Style: Styled.Wrapper });
