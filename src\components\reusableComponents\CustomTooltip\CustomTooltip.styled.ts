import styled from 'styled-components';

export const CustomTooltipContainer = styled.div`
    background: white;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
`;

export const TooltipColorIndicator = styled.div<{ color: string }>`
    width: 12px;
    height: 12px;
    background-color: ${({ color }) => color};
    border-radius: 2px;
`;

export const TooltipLabel = styled.span`
    font-weight: normal;
`;

export const TooltipValue = styled.span`
    font-weight: bold;
`;
