import { CustomTooltipContainer, TooltipColorIndicator, TooltipLabel, TooltipValue } from './CustomTooltip.styled';

export const CustomTooltip = ({ datum }: { datum: { id: string; value: number; color: string } }) => (
    <CustomTooltipContainer>
        <TooltipColorIndicator color={datum.color} />
        <TooltipLabel>{datum.id}:</TooltipLabel>
        <TooltipValue>{datum.value}</TooltipValue>
    </CustomTooltipContainer>
);
