import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const StyledText = styled(Text)`
    font-size: 1.4rem;
    color: ${({ theme }) => theme.contentColor};
`;

export const StyledAccordionWrapper = styled.div`
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
`;

export const StyledFAQItem = styled.div`
    margin-bottom: 2rem;
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
`;
