import { Accordion } from '@medly-components/core';
import { StyledAccordionTitle } from '@common';
import { StyledHead } from '@components/MetricsTable/MetricsTable.styled';
import { StyledAccordionWrapper, StyledFAQItem, StyledText } from './FAQAccordion.styled';
import React from 'react';
import { FAQAccordionProps } from './types';
import { TileTitle } from '../RatingsHeader/RatingsHeader.styled';

const FAQAccordion: React.FC<FAQAccordionProps> = ({ data }) => {
    return (
        <>
            {/* <ListHeader title="Frequently Asked Questions" /> */}
            <TileTitle textVariant="h4" style={{ marginTop: '2rem', marginBottom: '2rem', paddingLeft: '2rem' }}>
                Frequently Asked Questions (FAQs)
            </TileTitle>

            <StyledAccordionWrapper>
                {data.map((category, catIndex) => (
                    <Accordion key={catIndex}>
                        <StyledHead>
                            <StyledAccordionTitle textWeight="Medium">{category.categoryTitle}</StyledAccordionTitle>
                        </StyledHead>

                        <Accordion.Content>
                            <div style={{ padding: '2rem', paddingLeft: '3rem' }}>
                                {category.faqs.map((faq, faqIndex) => (
                                    <StyledFAQItem key={faqIndex}>
                                        <StyledText textVariant="h4" textWeight="Medium">
                                            {faq.question}
                                        </StyledText>
                                        <StyledText>{faq.answer}</StyledText>
                                    </StyledFAQItem>
                                ))}
                            </div>
                        </Accordion.Content>
                    </Accordion>
                ))}
            </StyledAccordionWrapper>
        </>
    );
};

export default FAQAccordion;
