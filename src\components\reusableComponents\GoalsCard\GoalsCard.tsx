import { goalSectionConstants, goalsProgressOptions, goalsTypesOptions } from '@constants/data';
import React, { useMemo } from 'react';
import { GoalsCardItem, CountDisplay, GoalLabel } from './GoalsCard.styled';
import { GoalsCardProps } from './types';
import { GoalsCardContainer } from '../DashboardActionItems/DashboardActionItems.styled';

export const GoalsCard: React.FC<GoalsCardProps> = ({ goalTypeId, count, showZeroCount = true, section }) => {
    const goalOption = useMemo(() => {
        const options = section === goalSectionConstants.GoalTypes ? goalsTypesOptions : goalsProgressOptions;
        return options.find(option => option.id === goalTypeId);
    }, [goalTypeId, section]);

    if (!goalOption || (count === 0 && !showZeroCount)) {
        return null;
    }

    return (
        <GoalsCardItem backgroundColor={goalOption.color}>
            <CountDisplay color={goalOption.titleColor}>{count}</CountDisplay>
            <GoalLabel>{goalOption.label}</GoalLabel>
        </GoalsCardItem>
    );
};

export const GoalsCardsContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return <GoalsCardContainer>{children}</GoalsCardContainer>;
};
