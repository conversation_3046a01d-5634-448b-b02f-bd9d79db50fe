import { GoalsResponse } from '@pages/Goals/types';

export const myGoalsMock: GoalsResponse = {
    totalGoals: 3,
    goals: [
        {
            id: 6,
            goalId: 'GL-4',
            description: 'Goal for testing - 2',
            typeId: 1,
            createdBy: 49,
            assignedTo: 1,
            progressId: 3,
            progressName: 'Completed',
            targetDate: 1740767400000
        },
        {
            id: 5,
            goalId: 'GL-2',
            description: 'Performance Metrics & Ratings Value Self Manager 1 Manager 2 Check-in Action Knowledge & Skills Growth',
            typeId: 1,
            createdBy: 4,
            assignedTo: 1,
            progressName: 'In Progress',
            progressId: 3,
            targetDate: 1740767400000
        },
        {
            id: 2,
            goalId: 'GL-1',
            description: 'Goal for testing - 1',
            typeId: 1,
            createdBy: 4,
            assignedTo: 1,
            progressId: 3,
            progressName: 'Completed',
            targetDate: 1740767400000
        }
    ]
};

export const teamGoalsMock: GoalsResponse = {
    totalGoals: 4,
    goals: [
        {
            id: 6,
            goalId: 'GL-4',
            description: 'Goal for testing - 2',
            typeId: 1,
            createdBy: 49,
            assignedTo: 1,
            progressId: 3,
            progressName: 'Completed',
            targetDate: 1740767400000
        },
        {
            id: 7,
            goalId: 'GL-3',
            description: 'Goal for testing - 3',
            typeId: 1,
            createdBy: 4,
            assignedTo: 4,
            progressId: 1,
            progressName: 'Pending',
            targetDate: 1740767400000
        },
        {
            id: 5,
            goalId: 'GL-2',
            description: 'Performance Metrics & Ratings Value Self Manager 1 Manager 2 Check-in Action Knowledge & Skills Growth',
            typeId: 1,
            createdBy: 4,
            assignedTo: 1,
            progressId: 3,
            progressName: 'Completed',
            targetDate: 1740767400000
        },
        {
            id: 2,
            goalId: 'GL-1',
            description: 'Goal for testing - 1',
            typeId: 1,
            createdBy: 4,
            assignedTo: 1,
            progressId: 3,
            progressName: 'Completed',
            targetDate: 1740767400000
        }
    ]
};
