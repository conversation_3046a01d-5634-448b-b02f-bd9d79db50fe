import { Loader, PageContent } from '@components';
import { CustomTable } from '@components/reusableComponents/CustomTable/CustomTable';
import ListHeader from '@components/reusableComponents/ListHeader';
import { Text } from '@medly-components/core';
import { appFeedbackColumns } from './columns';
import { useAppFeedback } from './useAppFeedback';

export const AppFeedback = () => {
    const { data, isLoading, handlePageChange, activePage = '1' } = useAppFeedback();
    if (isLoading) {
        return <Loader />;
    }
    return (
        <PageContent>
            <ListHeader
                title={
                    <Text textVariant="h3" textWeight="Medium">
                        App Feedback
                    </Text>
                }
            />
            <CustomTable
                data={data?.feedbacks || []}
                columns={appFeedbackColumns}
                isLoading={isLoading}
                handlePageChange={handlePageChange}
                count={data?.totalFeedbacks}
                activePage={parseInt(activePage)}
            />
        </PageContent>
    );
};
