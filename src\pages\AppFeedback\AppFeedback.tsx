import { Loader, PageContent } from '@components';
import { CustomTable } from '@components/reusableComponents/CustomTable/CustomTable';
import ListHeader from '@components/reusableComponents/ListHeader';
import { Text } from '@medly-components/core';
import { AppFeedbackModal } from './AppFeedbackModal';
import { AppFeedbackModalProvider, useAppFeedbackModal } from './AppFeedbackModalContext';
import { appFeedbackColumns } from './columns';
import { useAppFeedback } from './useAppFeedback';

const AppFeedbackContent = () => {
    const { data, isLoading, handlePageChange, activePage = '1' } = useAppFeedback();
    const { isOpen, selectedFeedback, closeModal } = useAppFeedbackModal();

    if (isLoading) {
        return <Loader />;
    }

    return (
        <PageContent>
            <ListHeader
                title={
                    <Text textVariant="h3" textWeight="Medium">
                        App Feedback
                    </Text>
                }
            />
            <CustomTable
                data={data?.feedbacks || []}
                columns={appFeedbackColumns}
                isLoading={isLoading}
                handlePageChange={handlePageChange}
                count={data?.totalFeedbacks}
                activePage={parseInt(activePage)}
            />
            <AppFeedbackModal isOpen={isOpen} onClose={closeModal} feedbackData={selectedFeedback} />
        </PageContent>
    );
};

export const AppFeedback = () => {
    return (
        <AppFeedbackModalProvider>
            <AppFeedbackContent />
        </AppFeedbackModalProvider>
    );
};
