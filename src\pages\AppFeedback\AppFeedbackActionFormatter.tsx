import { ColumnActionText } from '@common';
import { TableColumnConfig } from '@medly-components/core';
import { useAppFeedbackModal } from './AppFeedbackModalContext';
import { Feedback } from './types';

export const AppFeedbackActionFormatter: TableColumnConfig['component'] = ({ rowData }) => {
    const { openModal } = useAppFeedbackModal();

    const handleClick = () => {
        if (rowData) {
            openModal(rowData as Feedback);
        }
    };

    return <ColumnActionText onClick={handleClick}>View</ColumnActionText>;
};
