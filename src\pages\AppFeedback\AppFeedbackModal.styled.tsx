import { StyledModal } from '@common';
import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const StyledPopup = styled(StyledModal)`
    #medly-modal-inner-container {
        width: 600px;
    }
    #medly-modal-inner-container #medly-modal-content {
        padding-bottom: 3rem;
    }
`;

export const StyledModalContentFlex = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
`;

export const FeedbackLayer = styled.div`
    display: flex;
    flex-direction: column;
`;

export const FeedbackDescriptionLayer = styled.div`
    display: flex;
    flex-direction: column;

    span > ul,
    p,
    ol {
        margin-top: 0;
        margin-bottom: 0;
    }
`;

export const StyledHeading = styled(Text).attrs({ textWeight: 'Medium', textVariant: 'h4' })`
    font-size: ${({ theme }) => theme.contentFontSize};
    color: ${({ theme }) => theme.contentColor};
    span {
        font-weight: 400;
    }
`;

export const StyledHTMLText = styled(Text)`
    p {
        word-wrap: break-word;
        margin-block-start: 0;
        font-size: ${({ theme }) => theme.contentFontSize};
        color: ${({ theme }) => theme.contentColor};
    }
`;

export const StyledText = styled(Text)`
    font-size: ${({ theme }) => theme.contentFontSize};
    color: ${({ theme }) => theme.contentColor};
    word-wrap: break-word;
    white-space: pre-wrap;
`;
