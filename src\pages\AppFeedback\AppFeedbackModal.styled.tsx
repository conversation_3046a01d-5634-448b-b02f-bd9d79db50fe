import { StyledModal } from '@common';
import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const StyledPopup = styled(StyledModal)`
    #medly-modal-inner-container {
        width: 800px;
    }
    #medly-modal-inner-container #medly-modal-content {
        padding-bottom: 3rem;
    }
`;

export const StyledModalContentFlex = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
`;

export const FeedbackLayer = styled.div`
    display: flex;
    flex-direction: column;
`;

export const FeedbackDescriptionLayer = styled.div`
    display: flex;
    flex-direction: column;

    span > ul,
    p,
    ol {
        margin-top: 0;
        margin-bottom: 0;
    }
`;

export const StyledHeading = styled(Text)`
    font-size: 1.3rem;
`;

export const StyledHTMLText = styled(Text)`
    width: 100%;
    p {
        word-wrap: break-word;
        margin-block-start: 0;
        font-size: 1.3rem;
    }
`;

export const StyledText = styled(Text)`
    font-size: 1.3rem;
    word-wrap: break-word;
    white-space: pre-wrap;
`;
