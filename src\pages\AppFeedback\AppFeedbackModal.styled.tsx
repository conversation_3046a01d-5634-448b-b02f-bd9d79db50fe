import { StyledModal } from '@common';
import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const StyledPopup = styled(StyledModal)`
    #medly-modal-inner-container {
        width: 600px;
    }
`;

export const StyledModalContentFlex = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
`;

export const FeedbackLayer = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 0.5rem;
`;

export const FeedbackDescriptionLayer = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 0.5rem;
    margin-top: 1rem;
`;

export const StyledHeading = styled(Text)`
    color: ${({ theme }) => theme.colors.grey[700]};
`;

export const StyledText = styled(Text)`
    color: ${({ theme }) => theme.colors.grey[600]};
    word-wrap: break-word;
    white-space: pre-wrap;
`;
