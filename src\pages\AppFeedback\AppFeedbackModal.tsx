import { StyledModalContent, StyledModalHeader, StyledModalTitle } from '@common';
import { format } from 'date-fns';
import { Feedback } from './types';
import {
    StyledPopup,
    StyledModalContentFlex,
    FeedbackLayer,
    StyledHeading,
    StyledText,
    FeedbackDescriptionLayer
} from './AppFeedbackModal.styled';

interface AppFeedbackModalProps {
    isOpen: boolean;
    onClose: () => void;
    feedbackData: Feedback | null;
}

export const AppFeedbackModal = ({ isOpen, onClose, feedbackData }: AppFeedbackModalProps) => {
    if (!feedbackData) return null;

    return (
        <StyledPopup open={isOpen} onCloseModal={onClose}>
            <StyledModalHeader>
                <StyledModalTitle textVariant="h2">App Feedback Details</StyledModalTitle>
            </StyledModalHeader>
            <StyledModalContent>
                <StyledModalContentFlex>
                    <FeedbackLayer>
                        <StyledHeading textWeight="Medium">
                            Date: <StyledText>{format(new Date(feedbackData.createdAt), 'dd/MM/yyyy')}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading textWeight="Medium">
                            Organization ID: <StyledText>{feedbackData.organisationId}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading textWeight="Medium">
                            Organization Name: <StyledText>{feedbackData.organisationName}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading textWeight="Medium">
                            Employee Name: <StyledText>{feedbackData.feedbackFromName}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    {/* <FeedbackLayer>
                        <StyledHeading textWeight="Medium">
                            Employee ID: <StyledText>{feedbackData.feedbackFromId}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer> */}
                    <FeedbackDescriptionLayer>
                        <StyledHeading textWeight="Medium">Feedback:</StyledHeading>
                        <StyledText>{feedbackData.feedback}</StyledText>
                    </FeedbackDescriptionLayer>
                </StyledModalContentFlex>
            </StyledModalContent>
        </StyledPopup>
    );
};
