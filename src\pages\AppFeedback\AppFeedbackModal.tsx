import { StyledModalContent, StyledModalHeader, StyledModalTitle } from '@common';
import { format } from 'date-fns';
import { Feedback } from './types';
import {
    StyledPopup,
    StyledModalContentFlex,
    FeedbackLayer,
    StyledHeading,
    StyledText,
    FeedbackDescriptionLayer
} from './AppFeedbackModal.styled';

interface AppFeedbackModalProps {
    isOpen: boolean;
    onClose: () => void;
    feedbackData: Feedback | null;
}

export const AppFeedbackModal = ({ isOpen, onClose, feedbackData }: AppFeedbackModalProps) => {
    if (!feedbackData) return null;

    return (
        <StyledPopup open={isOpen} onCloseModal={onClose}>
            <StyledModalHeader>
                <StyledModalTitle textVariant="h2">App Feedback Details</StyledModalTitle>
            </StyledModalHeader>
            <StyledModalContent>
                <StyledModalContentFlex>
                    <FeedbackLayer>
                        <StyledHeading>
                            Date: <StyledText>{format(new Date(feedbackData.createdAt), 'dd/MM/yyyy')}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading>
                            Organization ID: <StyledText>{feedbackData.organisationId}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading>
                            Organization Name: <StyledText>{feedbackData.organisationName}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading>
                            Employee Name: <StyledText>{feedbackData.feedbackFromName}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackLayer>
                        <StyledHeading>
                            Employee ID: <StyledText>{feedbackData.feedbackFromId}</StyledText>
                        </StyledHeading>
                    </FeedbackLayer>
                    <FeedbackDescriptionLayer>
                        <StyledHeading>Feedback:</StyledHeading>
                        <StyledText>{feedbackData.feedback}</StyledText>
                    </FeedbackDescriptionLayer>
                </StyledModalContentFlex>
            </StyledModalContent>
        </StyledPopup>
    );
};
