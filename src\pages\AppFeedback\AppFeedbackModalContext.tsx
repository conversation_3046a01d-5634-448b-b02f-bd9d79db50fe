import { createContext, useContext, useState, ReactNode } from 'react';
import { Feedback } from './types';

interface AppFeedbackModalContextType {
    isOpen: boolean;
    selectedFeedback: Feedback | null;
    openModal: (feedback: Feedback) => void;
    closeModal: () => void;
}

const AppFeedbackModalContext = createContext<AppFeedbackModalContextType | undefined>(undefined);

interface AppFeedbackModalProviderProps {
    children: ReactNode;
}

export const AppFeedbackModalProvider = ({ children }: AppFeedbackModalProviderProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);

    const openModal = (feedback: Feedback) => {
        setSelectedFeedback(feedback);
        setIsOpen(true);
    };

    const closeModal = () => {
        setIsOpen(false);
        setSelectedFeedback(null);
    };

    return (
        <AppFeedbackModalContext.Provider value={{ isOpen, selectedFeedback, openModal, closeModal }}>
            {children}
        </AppFeedbackModalContext.Provider>
    );
};

export const useAppFeedbackModal = (): AppFeedbackModalContextType => {
    const context = useContext(AppFeedbackModalContext);
    if (!context) {
        throw new Error('useAppFeedbackModal must be used within an AppFeedbackModalProvider');
    }
    return context;
};
