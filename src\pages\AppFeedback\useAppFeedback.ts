import { routeConstants } from '@constants';
import { useGetAppFeedbacksQuery } from '@slice/services';
import { useNavigate, useParams } from 'react-router-dom';

export const useAppFeedback = () => {
    const navigateTo = useNavigate();

    const { activePage } = useParams();

    const { data, isLoading } = useGetAppFeedbacksQuery(
        {
            path: '',
            params: [
                { name: 'limit', value: '10' },
                {
                    name: 'page',
                    value: activePage ?? 1
                }
            ]
        },
        { refetchOnMountOrArgChange: true }
    );

    const handlePageChange = (page: number) => {
        navigateTo(`${routeConstants.appFeedback}/${page}`);
    };

    return {
        data,
        isLoading,
        handlePageChange,
        activePage
    };
};
