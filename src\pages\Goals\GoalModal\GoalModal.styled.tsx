import { StyledModal } from '@common';
import { DatePicker, TextField } from '@medly-components/core';
import styled from 'styled-components';

export const StyledPopup = styled(StyledModal)`
    /* #medly-modal-popup {
        overflow-y: auto !important;
        width: 103.6rem;
    } */
    #medly-modal-inner-container #medly-modal-content {
        padding-bottom: 3rem;
    }
`;

export const StyledModalContentFlex = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
    max-width: 55rem;
`;

export const StyledModalContentGrid = styled.div`
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 2rem 1rem;
`;

export const StyledDatePicker = styled(DatePicker)`
    max-height: 45px;
    margin: 0;
    margin-bottom: 0.2rem;
    width: 25rem;
    flex-direction: inherit;

    div > div > input::placeholder,
    &:hover div > div > input::placeholder {
        color: transparent;
    }

    div > div > input {
        line-height: 1.9rem;
    }

    div > div > label {
        line-height: 2.1rem;
        color: #666 !important;
    }
`;

export const StyledButtonWrapper = styled.div`
    display: flex;
    justify-content: flex-end;
    margin-top: 2rem;
`;

export const StyledTextField = styled(TextField)<{ marginBottom?: boolean }>`
    margin-bottom: ${({ marginBottom }) => marginBottom && '3rem'};
    max-width: 25rem;
    div > div > input {
        line-height: 1.9rem;
        &::placeholder {
            color: transparent;
        }
    }
    div > div > label {
        line-height: 2.1rem;
        color: #666 !important;
    }
`;
