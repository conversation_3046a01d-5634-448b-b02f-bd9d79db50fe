import { StyledModalContent, StyledModalHeader, StyledModalTitle, StyledSingleSelect } from '@common';
import { SelectEmployee, useSelectEmployee } from '@components';
import ListHeader from '@components/reusableComponents/ListHeader';
import { Button } from '@medly-components/core';
import { useAppSelector } from '@slice';
import { useCallback, useMemo } from 'react';
import { calculateGoalPermissions } from './goalPermissions';
import {
    StyledButtonWrapper,
    StyledDatePicker,
    StyledModalContentFlex,
    StyledModalContentGrid,
    StyledPopup,
    StyledTextField
} from './GoalModal.styled';
import { useGoalModalContext } from './GoalModalContext';
import { useGoalModal } from './useGoalModal';
import ViewGoalContent from './ViewGoalContent';

interface GoalModalProps {
    teamGoalAccess: boolean;
    showAddButton?: boolean;
}

const GoalModal = ({ showAddButton = true, teamGoalAccess }: GoalModalProps) => {
    const { isOpen, action, isSelfGoal, openModal, currentGoal } = useGoalModalContext();

    const currentUser = useAppSelector(state => state.user);

    const { employeeList } = useSelectEmployee(true);

    const memoizedEmployeeList = useMemo(() => employeeList(), [employeeList]);

    const {
        description,
        errorState,
        handleDescriptionChange,
        goalTypeOptions,
        goalStatusOptions,
        goalProgress,
        goalType,
        goalOwner,
        handleGoalTypeChange,
        handleGoalProgressChange,
        handleSelectOwner,
        deadline,
        handleDeadlineChange,
        handleSubmitGoal,
        handleCloseModal,
        isLoading,
        isDirty
    } = useGoalModal(memoizedEmployeeList);

    // Team Goals Permission Logic
    // 1. Goal Creator == Authenticated User -> Can edit description (if goal progress is in TODO) and goal type (any status)
    // 2. Goal Owner == Authenticated User -> Can edit progress and goal type (any status)
    // 3. Owner && Creator != Authenticated User -> View only (read-only mode)
    const permissions = useMemo(() => {
        return calculateGoalPermissions({
            action,
            currentGoal,
            currentUserId: currentUser.id,
            isSelfGoal,
            goalProgress,
            goalStatusOptions,
            isDirty
        });
    }, [action, isSelfGoal, currentGoal, currentUser?.id, goalProgress, goalStatusOptions, isDirty]);

    const handleOpenAddModal = useCallback(() => {
        openModal(null, 'Add', true);
    }, [openModal]);

    return (
        <>
            {showAddButton && <ListHeader title="Goals" actionButtonLabel="Add Goal" actionButtonClick={handleOpenAddModal} />}

            {isOpen && (
                <StyledPopup open={isOpen} onCloseModal={handleCloseModal}>
                    <StyledModalHeader>
                        <StyledModalTitle textVariant="h2">{action} Goal</StyledModalTitle>
                    </StyledModalHeader>
                    <StyledModalContent>
                        <StyledModalContentFlex>
                            {action !== 'View' && (
                                <>
                                    <StyledModalContentGrid>
                                        <StyledSingleSelect
                                            options={goalTypeOptions.withoutAll}
                                            variant="outlined"
                                            placeholder="Select Type"
                                            label="Goal Type"
                                            onChange={handleGoalTypeChange}
                                            value={goalType}
                                            disabled={!permissions.canEditType}
                                            size="M"
                                            minWidth="100%"
                                            errorText={errorState.goalType || ''}
                                        />

                                        <StyledDatePicker
                                            label="Deadline"
                                            placeholder="Select Date"
                                            displayFormat="dd-MM-yyyy"
                                            value={deadline}
                                            onChange={handleDeadlineChange}
                                            disabled={action !== 'Add'}
                                            size="M"
                                            minSelectableDate={new Date()}
                                            minWidth="100%"
                                            required
                                            errorText={errorState.deadline || ''}
                                            variant="outlined"
                                            data-testid="deadline-picker"
                                        />
                                        <SelectEmployee
                                            placeholder="Select Owner Name"
                                            label="Owner"
                                            includeMe={true}
                                            value={goalOwner}
                                            onChange={handleSelectOwner}
                                            disabled={!permissions.canEditOwner || !teamGoalAccess}
                                            minWidth="100%"
                                            maxHeight="25rem"
                                            // errorText={errorState.goalOwner || ''}
                                        />

                                        <StyledSingleSelect
                                            options={goalStatusOptions.withoutAll}
                                            variant="outlined"
                                            placeholder="Select Progress"
                                            label="Goal Progress"
                                            onChange={handleGoalProgressChange}
                                            value={goalProgress}
                                            disabled={!permissions.canEditProgress}
                                            size="M"
                                            minWidth="100%"
                                        />
                                    </StyledModalContentGrid>
                                    <StyledTextField
                                        multiline
                                        withCharacterCount
                                        maxLength={150}
                                        name="goalDescription"
                                        autoFocus
                                        variant="outlined"
                                        size="M"
                                        label="Goal Description"
                                        // placeholder="Enter Goal description"
                                        minRows={3}
                                        type="text"
                                        value={description}
                                        onChange={handleDescriptionChange}
                                        onKeyDown={undefined}
                                        errorText={errorState.description || ''}
                                        minWidth="100%"
                                        disabled={!permissions.canEditDescription}
                                        data-testid="modalinput"
                                        className="no-max-height"
                                    />
                                </>
                            )}

                            {action === 'View' && <ViewGoalContent currentGoal={currentGoal} />}
                        </StyledModalContentFlex>
                        {action !== 'View' && (
                            <StyledButtonWrapper>
                                <Button
                                    variant="solid"
                                    isLoading={isLoading}
                                    onClick={handleSubmitGoal}
                                    disabled={!permissions.canSubmit}
                                    data-testid="submit-goal-button"
                                >
                                    {action === 'Add' ? 'Submit' : 'Save'}
                                </Button>
                            </StyledButtonWrapper>
                        )}
                    </StyledModalContent>
                </StyledPopup>
            )}
        </>
    );
};

export default GoalModal;
