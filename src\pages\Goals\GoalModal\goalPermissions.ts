import { GoalData, ModalAction } from '../types';
import { Option } from '@components/overrides/SingleSelect/types';

export interface GoalPermissions {
    canEditDescription: boolean;
    canEditType: boolean;
    canEditOwner: boolean;
    canEditProgress: boolean;
    canSubmit: boolean;
}

export interface PermissionContext {
    action: ModalAction;
    currentGoal: GoalData | null;
    currentUserId: string | number;
    isSelfGoal: boolean;
    goalProgress: number;
    goalStatusOptions: { withoutAll: Option[] };
    isDirty: boolean;
}

export enum UserRole {
    CREATOR_ONLY = 'creator_only',
    OWNER_ONLY = 'owner_only',
    CREATOR_AND_OWNER = 'creator_and_owner',
    NEITHER = 'neither'
}

const TODO_LABEL = 'To Do';

export const getUserRole = (currentGoal: GoalData | null, currentUserId: string | number): UserRole => {
    if (!currentGoal) return UserRole.NEITHER;

    const isCreator = currentGoal.createdBy === Number(currentUserId);
    const isOwner = currentGoal.assignedTo === Number(currentUserId);

    if (isCreator && isOwner) return UserRole.CREATOR_AND_OWNER;
    if (isCreator && !isOwner) return UserRole.CREATOR_ONLY;
    if (!isCreator && isOwner) return UserRole.OWNER_ONLY;
    return UserRole.NEITHER;
};

export const isGoalInTodoStatus = (goalProgress: number, goalStatusOptions: { withoutAll: Option[] }): boolean => {
    const todoOption = goalStatusOptions.withoutAll.find(option => option.value === goalProgress && option.label === TODO_LABEL);
    return Boolean(todoOption);
};

export const getAddGoalPermissions = (isSelfGoal: boolean, isDirty: boolean): GoalPermissions => ({
    canEditDescription: isSelfGoal,
    canEditType: isSelfGoal,
    canEditOwner: isSelfGoal,
    canEditProgress: false, // Progress is always disabled for new goals
    canSubmit: isSelfGoal && isDirty
});

export const getNoGoalPermissions = (): GoalPermissions => ({
    canEditDescription: false,
    canEditType: false,
    canEditOwner: false,
    canEditProgress: false,
    canSubmit: false
});

export const getCreatorOnlyPermissions = (isGoalInTodo: boolean, isDirty: boolean): GoalPermissions => ({
    canEditDescription: isGoalInTodo,
    canEditType: true, // Creators can always edit goal type regardless of status
    canEditOwner: false,
    canEditProgress: false,
    canSubmit: isDirty // Creators can submit if form is dirty (for goal type changes in any status)
});

export const getOwnerOnlyPermissions = (isDirty: boolean): GoalPermissions => ({
    canEditDescription: false,
    canEditType: true,
    canEditOwner: false,
    canEditProgress: true,
    canSubmit: isDirty
});

export const getCreatorAndOwnerPermissions = (isGoalInTodo: boolean, isDirty: boolean): GoalPermissions => ({
    canEditDescription: isGoalInTodo,
    canEditType: true,
    canEditOwner: false,
    canEditProgress: true,
    canSubmit: isDirty
});

export const getViewOnlyPermissions = (): GoalPermissions => ({
    canEditDescription: false,
    canEditType: false,
    canEditOwner: false,
    canEditProgress: false,
    canSubmit: false
});

export const canUserEditGoal = (userRole: UserRole): boolean => {
    return userRole !== UserRole.NEITHER;
};

export const canUserEditDescription = (userRole: UserRole, isGoalInTodo: boolean): boolean => {
    return (userRole === UserRole.CREATOR_ONLY || userRole === UserRole.CREATOR_AND_OWNER) && isGoalInTodo;
};

export const canUserEditType = (userRole: UserRole): boolean => {
    return userRole === UserRole.CREATOR_ONLY || userRole === UserRole.OWNER_ONLY || userRole === UserRole.CREATOR_AND_OWNER;
};

export const canUserEditProgress = (userRole: UserRole): boolean => {
    return userRole === UserRole.OWNER_ONLY || userRole === UserRole.CREATOR_AND_OWNER;
};

export const calculateGoalPermissions = (context: PermissionContext): GoalPermissions => {
    const { action, currentGoal, currentUserId, isSelfGoal, goalProgress, goalStatusOptions, isDirty } = context;

    // Handle Add action
    if (action === 'Add') {
        return getAddGoalPermissions(isSelfGoal, isDirty);
    }

    // Handle case when no goal is available
    if (!currentGoal) {
        return getNoGoalPermissions();
    }

    // Determine user role and goal status
    const userRole = getUserRole(currentGoal, currentUserId);
    const isGoalInTodo = isGoalInTodoStatus(goalProgress, goalStatusOptions);

    // Return permissions based on user role
    switch (userRole) {
        case UserRole.CREATOR_ONLY:
            return getCreatorOnlyPermissions(isGoalInTodo, isDirty);

        case UserRole.OWNER_ONLY:
            return getOwnerOnlyPermissions(isDirty);

        case UserRole.CREATOR_AND_OWNER:
            return getCreatorAndOwnerPermissions(isGoalInTodo, isDirty);

        case UserRole.NEITHER:
        default:
            return getViewOnlyPermissions();
    }
};
