import { ALL_OPTION } from '@constants/data';
import { Option } from '@medly-components/core/dist/es/components/SearchBox/types';
import { useAddGoalMutation, useGetGoalProgressListQuery, useGetGoalTypesQuery, useUpdateGoalMutation } from '@slice/services';
import { SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { GoalProgress, GoalType } from '../types';
import { useGoalModalContext } from './GoalModalContext';
import { addToast } from '@medly-components/core';

export const useGoalModal = (employeeList: Option[]) => {
    const { isOpen, currentGoal, action, closeModal } = useGoalModalContext();

    const [goalProgress, setGoalProgress] = useState(0);
    const [goalType, setGoalType] = useState(0);
    const [goalOwner, setGoalOwner] = useState(-99);
    const [description, setDescription] = useState('');
    const [errorState, setErrorState] = useState({
        description: '',
        progress: '',
        deadline: '',
        goalOwner: '',
        goalType: ''
    });
    const [deadline, setDeadline] = useState<Date | null>(null);

    // Track original values for change detection
    const [originalValues, setOriginalValues] = useState({
        description: '',
        typeId: 0,
        assignedTo: -99,
        progressId: 0,
        targetDate: null as Date | null
    });

    const [createGoal, { isLoading: isCreatingGoal, isSuccess: isAddSuccess }] = useAddGoalMutation();
    const [editGoal, { isLoading: isEditingGoal, isSuccess: isEditSuccess }] = useUpdateGoalMutation();

    const { data: goalTypesData } = useGetGoalTypesQuery({
        path: '',
        params: []
    });

    const goalTypeOptions = useMemo(() => {
        const baseOptions = (goalTypesData ?? []).map((item: GoalType) => ({
            value: item.typeId,
            label: item.typeName
        }));

        return {
            withAll: [ALL_OPTION, ...baseOptions],
            withoutAll: baseOptions
        };
    }, [goalTypesData]);

    const { data: goalProgressData } = useGetGoalProgressListQuery({
        path: '',
        params: []
    });

    const goalStatusOptions = useMemo(() => {
        const baseOptions: Option[] = (goalProgressData ?? []).map((item: GoalProgress) => ({
            value: item.progressId,
            label: item.progressName
        }));

        return {
            withAll: [ALL_OPTION, ...baseOptions],
            withoutAll: baseOptions
        };
    }, [goalProgressData]);

    // Initialize form data when modal opens or current goal changes
    useEffect(() => {
        if (isOpen) {
            if ((action === 'Edit' || action === 'View') && currentGoal) {
                const desc = currentGoal.description || '';
                const typeId = currentGoal.typeId || 0;
                const assignedTo = currentGoal.assignedTo || -99;
                const progressId = currentGoal.progressId || 0;
                const targetDate = currentGoal.targetDate ? new Date(currentGoal.targetDate) : null;

                // Set form values
                setDescription(desc);
                setGoalType(typeId);
                setGoalOwner(assignedTo);
                setGoalProgress(progressId);
                setDeadline(targetDate);

                // Store original values for change detection
                setOriginalValues({
                    description: desc,
                    typeId,
                    assignedTo,
                    progressId,
                    targetDate
                });
            } else if (action === 'Add') {
                // Reset form for adding new goal
                const toDoOption = goalStatusOptions.withoutAll.find(item => item.label === 'To Do');
                setDescription('');
                setGoalOwner(employeeList.length > 0 ? Number(employeeList[0].value) : -99);
                setGoalProgress(Number(toDoOption?.value) ?? 0);
                setGoalType(1);
                setDeadline(null);

                // Clear original values for new goals
                setOriginalValues({
                    description: '',
                    typeId: 0,
                    assignedTo: -99,
                    progressId: 0,
                    targetDate: null
                });
            }

            // Clear any previous errors
            setErrorState({
                description: '',
                progress: '',
                deadline: '',
                goalOwner: '',
                goalType: ''
            });
        }
    }, [isOpen, action, currentGoal, goalStatusOptions.withoutAll, employeeList]);

    // Reset form when modal closes
    useEffect(() => {
        if (!isOpen) {
            setDescription('');
            setDeadline(null);
            setGoalProgress(0);
            setGoalType(0);
            setGoalOwner(-99);
            setErrorState({
                description: '',
                progress: '',
                deadline: '',
                goalOwner: '',
                goalType: ''
            });
        }
    }, [isOpen]);

    const handleGoalProgressChange = useCallback((value: number) => {
        setGoalProgress(value);
    }, []);

    const handleGoalTypeChange = useCallback((value: number) => {
        setGoalType(value);

        // Validate goal type immediately
        if (!value) {
            setErrorState(prev => ({ ...prev, goalType: 'Goal type is required' }));
        } else {
            setErrorState(prev => ({ ...prev, goalType: '' }));
        }
    }, []);

    const handleSelectOwner = useCallback((value: SetStateAction<string | number>) => {
        const ownerValue = Number(value);
        setGoalOwner(ownerValue);

        // Validate goal owner immediately
        if (!ownerValue || ownerValue === -99) {
            setErrorState(prev => ({ ...prev, goalOwner: 'Goal owner is required' }));
        } else {
            setErrorState(prev => ({ ...prev, goalOwner: '' }));
        }
    }, []);

    const handleDeadlineChange = useCallback((date: Date | null) => {
        setDeadline(date);

        // Validate deadline immediately
        if (!date) {
            setErrorState(prev => ({ ...prev, deadline: 'Deadline is required' }));
        } else {
            setErrorState(prev => ({ ...prev, deadline: '' }));
        }
    }, []);

    const handleDescriptionChange = useCallback(({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
        setDescription(value);
        if (value.trim().length < 10) {
            setErrorState(prev => ({ ...prev, description: 'Goal description should be at least 10 characters' }));
        } else {
            setErrorState(prev => ({ ...prev, description: '' }));
        }
    }, []);

    // Helper function to get only changed values
    const getChangedValues = useCallback(() => {
        const changes: any = { goalId: currentGoal?.id };

        // Check each field for changes
        if (description.trim() !== originalValues.description) {
            changes.description = description.trim();
        }

        if (goalType !== originalValues.typeId) {
            changes.typeId = goalType;
        }

        if (goalOwner !== originalValues.assignedTo) {
            changes.assignedTo = goalOwner;
        }

        if (goalProgress !== originalValues.progressId) {
            changes.progressId = goalProgress;
        }

        // Compare dates properly
        const currentDateString = deadline?.toISOString() || '';
        const originalDateString = originalValues.targetDate?.toISOString() || '';
        if (currentDateString !== originalDateString) {
            changes.targetDate = currentDateString;
        }

        return changes;
    }, [description, goalType, goalOwner, goalProgress, deadline, originalValues, currentGoal?.id]);

    const handleCloseModal = useCallback(() => {
        closeModal();
    }, [closeModal]);

    const isLoading = isCreatingGoal || isEditingGoal;

    const isSuccess = isAddSuccess || isEditSuccess;

    const isFormValid = useMemo(() => {
        // For both 'Add' and 'Edit' modes, check content and errors.
        return (
            !errorState.description &&
            !errorState.goalType &&
            !errorState.goalOwner &&
            !errorState.deadline &&
            description.trim().length >= 10 &&
            goalType > 0 &&
            goalOwner > 0 &&
            goalOwner !== -99 &&
            deadline !== null
        );
    }, [errorState, description, goalType, goalOwner, deadline]);

    // Check if any field has been modified from its original value
    const isDirty = useMemo(() => {
        if (action === 'Add') {
            return isFormValid;
        }

        // For edit mode, check if any field has changed AND form is valid
        const currentDateString = deadline?.toISOString() || '';
        const originalDateString = originalValues.targetDate?.toISOString() || '';

        const hasChanges =
            description.trim() !== originalValues.description ||
            goalType !== originalValues.typeId ||
            goalOwner !== originalValues.assignedTo ||
            goalProgress !== originalValues.progressId ||
            currentDateString !== originalDateString;

        return hasChanges && isFormValid;
    }, [action, description, goalType, goalOwner, goalProgress, deadline, originalValues, isFormValid]);

    const validateForm = useCallback((): boolean => {
        // Since we're now validating on change, just check if form is valid
        return isFormValid;
    }, [isFormValid]);

    const handleSubmitGoal = useCallback(async () => {
        // Prevent multiple submissions
        if (isCreatingGoal || isEditingGoal) return;

        if (!validateForm()) return;

        try {
            if (action === 'Add') {
                const goalData = {
                    typeId: goalType,
                    description: description.trim(),
                    assignedTo: goalOwner,
                    targetDate: deadline?.toISOString() || ''
                };
                await createGoal(goalData).unwrap();
                addToast({
                    variant: 'success',
                    header: 'Goal created successfully',
                    timer: 3000
                });
            } else if (action === 'Edit' && currentGoal?.id) {
                const changedValues = getChangedValues();
                if (Object.keys(changedValues).length > 1) {
                    await editGoal(changedValues).unwrap();
                }
                addToast({
                    variant: 'success',
                    header: 'Goal updated successfully',
                    timer: 3000
                });
            }

            closeModal();
        } catch (error) {
            console.error('Failed to submit goal:', error);
            addToast({
                variant: 'error',
                header: 'Something went wrong. Please try again.',
                timer: 3000
            });
        }
    }, [
        isCreatingGoal,
        isEditingGoal,
        validateForm,
        goalType,
        description,
        goalOwner,
        deadline,
        action,
        currentGoal?.id,
        createGoal,
        editGoal,
        closeModal,
        getChangedValues
    ]);

    return {
        // Modal state from context
        isOpen,
        action,
        currentGoal,

        // Form state
        goalProgress,
        goalType,
        goalOwner,
        description,
        deadline,
        errorState,

        // Form handlers
        handleGoalProgressChange,
        handleGoalTypeChange,
        handleSelectOwner,
        handleDeadlineChange,
        handleDescriptionChange,
        handleSubmitGoal,
        handleCloseModal,

        // Options
        goalTypeOptions,
        goalStatusOptions,

        // Loading states
        isLoading,
        isSuccess,

        // Validation
        validateForm,

        // Change detection
        isDirty
    };
};
