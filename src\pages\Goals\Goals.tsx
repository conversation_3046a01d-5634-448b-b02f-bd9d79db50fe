import { StyledMultiSelect, StyledSingleSelect } from '@common';
import { PageContent } from '@components';
import { CustomTable } from '@components/reusableComponents/CustomTable/CustomTable';
import TooltipDropdown from '@components/reusableComponents/TooltipDropdown';
import { OptionsType } from '@components/reusableComponents/TooltipDropdown/types';
import { Tabs } from '@medly-components/core';
import { GroupIcon, PersonIcon } from '@medly-components/icons';
import { MyGoalsCols, TeamGoalsCols } from './columns';
import GoalModal from './GoalModal/GoalModal';
import { GoalModalProvider } from './GoalModal/GoalModalContext';
import { FilterContainer, GoalFilterWrapper, HeaderContainer, StyledTableWrapper, StyledTabs } from './Goals.styled';
import { useGoals } from './useGoals';

export const Goals = () => {
    const {
        handleTabChange,
        goalsData,
        totalGoalsDataCount,
        handlePageChange,
        page,
        activeTab,
        goalsDataIsLoading,
        goalStatusFilter,
        goalTypeFilter,
        goalOwner,
        handleGoalStatusChange,
        handleGoalTypeChange,
        handleSelectOwner,
        teamGoalsAccess,
        employeeList,
        goalTypeOptions,
        goalStatusOptions
    } = useGoals();

    return (
        <PageContent>
            <GoalModalProvider>
                <GoalModal showAddButton={true} teamGoalAccess={teamGoalsAccess} />
                <FilterContainer>
                    <GoalFilterWrapper>
                        {activeTab === 'teamGoals' && (
                            <>
                                <StyledMultiSelect
                                    options={employeeList}
                                    variant="outlined"
                                    placeholder="Select Owners"
                                    label="Goal Owner"
                                    onChange={handleSelectOwner}
                                    values={goalOwner}
                                    size="M"
                                    minWidth="25rem"
                                />
                                <TooltipDropdown
                                    dataIds={[`goal-owner-input`]}
                                    values={
                                        goalOwner?.length && employeeList.length
                                            ? employeeList.filter((item: OptionsType) => goalOwner?.includes(Number(item.value)))
                                            : []
                                    }
                                />
                            </>
                        )}
                        <StyledSingleSelect
                            options={goalTypeOptions.withAll}
                            variant="outlined"
                            placeholder="Select Type"
                            label="Goal Type"
                            onChange={val => val && handleGoalTypeChange(val)}
                            value={goalTypeFilter}
                            size="M"
                            minWidth="20rem"
                        />
                    </GoalFilterWrapper>
                    <StyledSingleSelect
                        options={goalStatusOptions.withAll}
                        variant="outlined"
                        placeholder="Select Progress"
                        label="Progress"
                        onChange={val => val && handleGoalStatusChange(val)}
                        value={goalStatusFilter}
                        size="M"
                        minWidth="20rem"
                    />
                </FilterContainer>
                <HeaderContainer>
                    <StyledTabs aria-label="Goals tabs" tabSize="M" variant="outlined" onChange={id => handleTabChange(id)}>
                        <Tabs.Tab active={activeTab === 'myGoals'} id="myGoals" label="My Goals" icon={PersonIcon} />
                        <Tabs.Tab
                            hide={!teamGoalsAccess}
                            active={activeTab === 'teamGoals'}
                            id="teamGoals"
                            label="Team Goals"
                            icon={GroupIcon}
                        />
                    </StyledTabs>
                </HeaderContainer>

                <StyledTableWrapper>
                    <CustomTable
                        data={goalsData || []}
                        tableKey={activeTab}
                        columns={activeTab === 'myGoals' ? MyGoalsCols : TeamGoalsCols}
                        isLoading={goalsDataIsLoading}
                        activePage={parseInt(page || '1')}
                        count={totalGoalsDataCount}
                        handlePageChange={handlePageChange}
                    />
                </StyledTableWrapper>
            </GoalModalProvider>
        </PageContent>
    );
};
