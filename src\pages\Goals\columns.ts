import { DateFormatter, GoalActionFormatter, GoalStatusFormatter } from '@components';
import { TableColumnConfig } from '@medly-components/core';

export const MyGoalsCols: TableColumnConfig[] = [
    // {
    //     field: 'goalId',
    //     title: 'ID'
    // },
    {
        field: 'description',
        title: 'Goal',
        fraction: 2.5
    },
    {
        field: 'typeName',
        title: 'Goal Type'
    },
    {
        field: 'createdByName',
        title: 'Created By'
    },
    {
        field: 'progressName',
        title: 'Progress',
        component: GoalStatusFormatter
    },
    {
        field: 'targetDate',
        title: 'Deadline',
        component: DateFormatter,
        sortable: true
    },
    {
        field: 'action',
        title: 'Action',
        component: GoalActionFormatter,
        fraction: 0.5
    }
];

export const TeamGoalsCols: TableColumnConfig[] = [
    {
        field: 'description',
        title: 'Goal',
        fraction: 2.5
    },
    {
        field: 'typeName',
        title: 'Goal Type'
    },
    {
        field: 'assignedToName',
        title: 'Goal Owner'
    },
    {
        field: 'createdByName',
        title: 'Created By'
    },
    {
        field: 'progressName',
        title: 'Progress',
        component: GoalStatusFormatter
    },
    {
        field: 'targetDate',
        title: 'Deadline',
        component: DateFormatter,
        sortable: true
    },
    {
        field: 'action',
        title: 'Action',
        component: GoalActionFormatter,
        fraction: 0.5
    }
];
