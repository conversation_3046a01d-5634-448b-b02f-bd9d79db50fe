export interface GoalData {
    goalId: string;
    id: number;
    description: string;
    typeId: number;
    createdBy: number;
    createdByName?: string;
    assignedToName?: string;
    assignedToEmployeeId?: string;
    createdByEmployeeId?: string;
    typeName?: string;
    assignedTo: number;
    progressId: number;
    targetDate: number; // timestamp in milliseconds
    progressName: string;
}

export interface GoalsResponse {
    totalGoals: number;
    goals: GoalData[];
}

export interface GoalType {
    typeId: number;
    typeName: string;
}

export interface GoalProgress {
    progressId: number;
    progressName: string;
}

export type ModalAction = 'Add' | 'View' | 'Edit';

export interface ModalState {
    action: ModalAction;
    open: boolean;
}

export type TabTypes = 'myGoals' | 'teamGoals';
