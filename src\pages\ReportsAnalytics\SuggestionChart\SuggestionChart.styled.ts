import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const ChartContainer = styled.div`
    display: flex;
    flex: 1;
    gap: 2rem;
    height: 100%;
`;

export const SuggestionCategoryWrapper = styled.div`
    display: flex;
    border: 1px solid #fff;
    background-color: ${({ theme }) => theme.customColors.anlyticsPageCardBackground};
    flex-direction: column;
    padding: ${({ theme }) => theme.spacing.M2};
    align-items: baseline;
    width: 68%;
    border-radius: 12px;
    gap: 2rem;

    > div:nth-child(2) {
        height: 20rem !important;
        @media (min-width: 1400px) {
            height: 26rem !important;
        }
    }
`;

export const SuggestionProgressWrapper = styled.div`
    display: flex;
    /* flex-grow: 1; */
    border: 1px solid #fff;
    background-color: ${({ theme }) => theme.customColors.anlyticsPageCardBackground};
    flex-direction: column;
    padding: 3rem;
    padding-bottom: 0;
    align-items: baseline;
    width: 32%;
    border-radius: 12px;
    gap: 4rem;
    height: 40rem;

    > div:nth-child(2) {
        height: 28rem !important;
        width: 100%;
        @media (min-width: 1400px) {
            height: 24rem !important;
        }
    }
`;

export const BarChartWrapper = styled.div`
    height: 34rem;
`;

export const TileTitle = styled(Text)`
    color: ${({ theme }) => theme.pageTitleColor};
    font-weight: 600;
`;

export const InfoSection = styled.div`
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.6rem;
    width: 100%;
    margin-top: 1rem;
`;

export const ColorScheme = styled.div`
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease;
    width: 10rem;
`;

export const Color = styled.div<{ bgColor: string }>`
    width: 1.1rem;
    height: 1.1rem;
    background-color: ${({ bgColor }) => bgColor};
    margin-right: 0.5rem;
    border-radius: 50%;
    border: 1px solid transparent;
`;

export const TypeText = styled(Text)`
    font-size: 1.2rem;
    font-weight: 500;
    line-height: 1.4;
`;

export const EmptyStateContainer = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 28rem;
    text-align: center;
    color: ${({ theme }) => theme.customColors.badgeTypoColor};

    @media (min-width: 1400px) {
        height: 32rem;
    }
`;

export const EmptyStateIcon = styled.div`
    width: 6rem;
    height: 6rem;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.customColors.badgeGreyBgColor};
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.6rem;
    color: ${({ theme }) => theme.customColors.ToDoColor};

    svg {
        width: 2.4rem;
        height: 2.4rem;
    }
`;

export const EmptyStateText = styled(Text)`
    font-size: 1.6rem;
    font-weight: 500;
    color: ${({ theme }) => theme.customColors.badgeTypoColor};
    margin-bottom: 0.8rem;
`;

export const EmptyStateSubText = styled(Text)`
    font-size: 1.3rem;
    color: ${({ theme }) => theme.customColors.ToDoColor};
`;

export const CategoriesGrid = styled.div`
    display: flex;
    flex-wrap: wrap;

    @media (min-width: 768px) {
        gap: 1rem 2rem;
    }
`;
