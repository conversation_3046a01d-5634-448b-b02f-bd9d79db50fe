import customColors from '@theme/core/colors';
import { SuggestionCategoryLabel, SuggestionProgressLabel } from './types';

export const suggestionCategoryColors = {
    'Workplace & Culture': customColors.positive,
    'Policies & Processes': customColors.viewBlueColor,
    'Learning & Development': customColors.exceedsExpectations,
    'Tools & Technology': customColors.appreciation,
    'Client & Customer Experience': customColors.improvement,
    'Innovation & New Product Ideas': customColors.consultantsColor,
    Others: customColors.ToDoColor
};

export const dataCategories: SuggestionCategoryLabel[] = [
    'Workplace & Culture',
    'Policies & Processes',
    'Learning & Development',
    'Tools & Technology',
    'Client & Customer Experience',
    'Innovation & New Product Ideas',
    'Others'
];

export const suggestionProgressColors = {
    'Under Review': customColors.ToDoColor,
    'In Progress': customColors.inProgressColor,
    Implemented: customColors.completedColor,
    Deferred: customColors.unacceptable
};

export const suggestionProgressCategories: SuggestionProgressLabel[] = ['Under Review', 'In Progress', 'Implemented', 'Deferred'];
