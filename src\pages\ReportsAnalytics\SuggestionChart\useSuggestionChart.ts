import { useAppSelector } from '@slice';
import { useGetSuggestionAnalyticsQuery } from '@slice/services';
import { useMemo } from 'react';
import { SuggestionCategoryItem } from './types';

export const useSuggestionChart = (reviewCycleId: string) => {
    const userDetails = useAppSelector(state => state.user);

    const { data, isLoading } = useGetSuggestionAnalyticsQuery(
        {
            path: '',
            params: [
                { name: 'organisationId', value: userDetails.organisationId },
                { name: 'reviewCycleId', value: reviewCycleId }
            ]
        },
        { refetchOnMountOrArgChange: true }
    );

    const suggestionCategory = useMemo(() => {
        if (data?.suggestionCategory) {
            return data?.suggestionCategory.map((item: SuggestionCategoryItem) => ({
                id: item.id,
                label: item.label,
                count: item.count,
                percentage: Number(item.percentage.toFixed(1))
            }));
        }
        return [];
    }, [data?.suggestionCategory]);

    const suggestionProgress = useMemo(() => {
        if (data?.suggestionProgress) {
            return data?.suggestionProgress.map((item: any) => ({
                label: item.label,
                count: item.count,
                [item.label]: item.count
            }));
        }
        return [];
    }, [data?.suggestionProgress]);

    return {
        suggestionCategory,
        suggestionProgress,
        isLoading
    };
};
