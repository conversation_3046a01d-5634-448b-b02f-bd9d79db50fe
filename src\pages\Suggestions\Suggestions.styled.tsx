import { ParentStyledTabs, StyledModal, StyledMultiSelect } from '@common';
import { Button, Text, TextField } from '@medly-components/core';
import styled from 'styled-components';

export const StyledTabs = styled(ParentStyledTabs)`
    margin-top: 2rem;
    button {
        width: 21rem;
    }
    div[role='tabpanel'] {
        display: none;
    }

    button[aria-selected='false'] div div span {
        color: #666;
    }

    button[aria-selected='false'] svg path {
        fill: #666;
    }

    button[aria-selected='false'] div div span:nth-of-type(2) {
        color: #666;
        background-color: ${({ theme }) => theme.colors.grey[200]};
    }

    button[aria-selected='false']:hover div div span:nth-of-type(2) {
        background-color: ${({ theme }) => theme.colors.grey[200]};
    }
`;

export const StyledTableWrapper = styled('div')`
    table {
        margin-top: 0;
        border-radius: 0 0.8rem 0.8rem 0.8rem;
    }
`;
export const StyledDiv = styled('div')``;

export const StyledButton = styled(Button)`
    width: fit-content;
`;

export const StyledSelect = styled(StyledMultiSelect)`
    margin-right: 1rem;
`;

export const StyledModalContentFlex = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 0.5rem;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 1.5rem;
    padding-left: 0.5rem;
    height: 56vh;

    @media (max-width: 1440px) {
        height: 68vh;
    }
`;

export const StyledPopup = styled(StyledModal)`
    #medly-modal-popup {
        width: 106rem;
        min-height: 60rem;
    }
`;

export const SuggestionLayer = styled.div`
    display: flex;
    flex-direction: column;
`;

export const SuggestionDescriptionLayer = styled.div`
    display: flex;
    flex-direction: column;
    row-gap: 1rem;

    span > ul,
    p,
    ol {
        margin-top: 0;
        margin-bottom: 0;
    }
`;

export const StyledHeading = styled(Text)`
    font-size: 1.3rem;
`;

export const StyledText = styled(Text)`
    font-size: 1.3rem;
`;

export const StyledHTMLText = styled(Text)`
    width: 100%;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    max-width: 100%;

    p {
        word-wrap: break-word;
        word-break: break-word;
        overflow-wrap: break-word;
        margin-block-start: 0;
        font-size: 1.3rem;
        white-space: pre-wrap;
        max-width: 100%;
    }
`;

export const CommentTextField = styled(TextField)`
    width: 100%;

    div > div > textarea {
        &::placeholder {
            color: transparent;
        }
    }

    div > div > label {
        line-height: 2.1rem;
        color: #666 !important;
    }

    #medly-textField-helper-text {
        position: absolute;
        bottom: -2.4rem;
    }
`;

export const CommentLimitText = styled('p')`
    text-align: right;
    font-size: 1.2rem;
    margin-top: 0;
    color: ${({ theme }) => theme.colors.grey[600]};
`;

export const SuggestionCommentContainer = styled.div``;

export const ProgressWrapper = styled.div`
    min-width: 20rem;
`;

export const CommentWrapper = styled.div`
    flex: 1;
    min-width: 25rem;
    display: flex;
    flex-direction: column;
`;

export const ProgressAndCommentWrapper = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1.8rem;
    width: 100%;
    margin-top: 1rem;

    @media (max-width: 768px) {
        flex-direction: column;

        & > ${ProgressWrapper}, & > ${CommentWrapper} {
            flex: 1 1 100%;
            min-width: 100%;
        }
    }
`;

export const FilterWrapper = styled.div`
    display: flex;
    flex-direction: row;
    gap: 1rem;
`;

export const SuggestionCommentsSection = styled(SuggestionDescriptionLayer)`
    max-height: 8rem;
    padding-right: 1.5rem;
`;

export const ActionsWrapper = styled.div`
    height: 8rem;
    padding-right: 1.5rem;
    button {
        margin-top: -1rem;
    }
`;
