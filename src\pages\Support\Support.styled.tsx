import { BadgeWrapper } from '@components/reusableComponents/Badge/Badge.styled';
import { Box, TextField } from '@medly-components/core';
import styled from 'styled-components';

export const SupportCardContainer = styled(Box)`
    display: flex;
    flex-direction: column;
    background-color: transparent;
    gap: 2rem;
    padding: 0;
`;

export const SupportCard = styled(BadgeWrapper)`
    display: flex;
    position: relative;
    align-items: center;
    flex-direction: row;
    gap: 1rem;
    border-radius: 0.5rem;
    height: 14rem;
    padding: 0 1rem;
    flex: 1 1 0;
    background-color: transparent;
    color: #555;
`;

export const SupportWrapper = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 2rem;
`;

export const StyledTextField = styled(TextField)<{ marginBottom?: boolean }>`
    margin-bottom: ${({ marginBottom }) => marginBottom && '3rem'};
    max-height: 15.5rem;
    max-width: 25rem;
    div > div > input {
        line-height: 1.9rem;
        &::placeholder {
            color: transparent;
        }
    }
    div > div > label {
        line-height: 2.1rem;
        color: #666 !important;
    }
`;

export const FeedbackContainer = styled.div`
    width: 55%;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background-color: #fafafa;
    padding: 2rem;
`;

export const SupportInputContainer = styled.div`
    #medly-textField-helper-text {
        position: absolute;
        bottom: -2.4rem;
    }
`;
