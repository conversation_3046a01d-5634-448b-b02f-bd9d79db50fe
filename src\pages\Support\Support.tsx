import { PageContent } from '@components/layout/PageContent/PageContent.styled';
import FAQAccordion from '@components/reusableComponents/FAQAccordion/FAQAccordion';
import ListHeader from '@components/reusableComponents/ListHeader';
import { TileTitle } from '@components/reusableComponents/RatingsHeader/RatingsHeader.styled';
import { Button } from '@medly-components/core';
import { SendIcon } from '@medly-components/icons';
import { FAQ_DATA, MIN_FEEDBACK_LENGTH, SUPPORT_CONTACTS } from './constants';
import { FeedbackContainer, StyledTextField, SupportCardContainer, SupportWrapper, SupportInputContainer } from './Support.styled';
import { SupportContactCard } from './SupportContactCard';
import { useSupport } from './useSupport';

export const SupportPage = () => {
    const { handleFeedbackSubmit, feedback, error, handleFeedbackChange, isLoading } = useSupport();

    return (
        <PageContent>
            <ListHeader title="Support" />
            <SupportWrapper>
                <div style={{ width: '45%', minWidth: '40rem', backgroundColor: '#fafafa', padding: '2rem' }}>
                    <TileTitle textVariant="h4" style={{ marginBottom: '3rem' }}>
                        Get Help
                    </TileTitle>
                    <SupportCardContainer>
                        {SUPPORT_CONTACTS.map(contact => (
                            <SupportContactCard key={contact.key} contact={contact} />
                        ))}
                    </SupportCardContainer>
                </div>

                {/* Feedback section */}
                <FeedbackContainer>
                    <TileTitle textVariant="h4" style={{ marginBottom: '1rem' }}>
                        SkillWatch Feedback
                    </TileTitle>
                    <SupportInputContainer>
                        <StyledTextField
                            label="Tell us what you think about SkillWatch"
                            variant="outlined"
                            value={feedback}
                            onChange={handleFeedbackChange}
                            errorText={error}
                            multiline
                            minWidth="100%"
                            minRows={20}
                            minLength={MIN_FEEDBACK_LENGTH}
                        />
                    </SupportInputContainer>
                    <Button
                        variant="outlined"
                        style={{ marginLeft: 'auto', marginTop: '0' }}
                        onClick={handleFeedbackSubmit}
                        isLoading={isLoading}
                        disabled={error.length > 0 || !feedback.trim() || isLoading}
                    >
                        <SendIcon size="S" />
                        Send
                    </Button>
                </FeedbackContainer>
            </SupportWrapper>

            <FAQAccordion data={FAQ_DATA} />
        </PageContent>
    );
};
