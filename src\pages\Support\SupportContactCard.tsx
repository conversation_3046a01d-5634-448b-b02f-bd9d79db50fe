import { StyledRatingCardText } from '@components/reusableComponents/RatingsHeader/RatingsHeader.styled';
import { Text } from '@medly-components/core';
import { FC } from 'react';
import { SupportCard } from './Support.styled';
import { TSupportContact } from './types';

export const SupportContactCard: FC<{ contact: TSupportContact }> = ({ contact }) => {
    const { icon, label, value, onClick, textVariant = 'h4', textAlign = 'left' } = contact;

    return (
        <SupportCard>
            <StyledRatingCardText textWeight="Medium" style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                {icon} {label}
            </StyledRatingCardText>

            <Text
                textVariant={textVariant}
                textWeight="Strong"
                textAlign={textAlign}
                onClick={onClick}
                style={{
                    cursor: onClick ? 'pointer' : 'auto',
                    color: '#08439b',
                    fontSize: '1.6rem',
                    fontWeight: 400
                }}
            >
                {value}
            </Text>
        </SupportCard>
    );
};
