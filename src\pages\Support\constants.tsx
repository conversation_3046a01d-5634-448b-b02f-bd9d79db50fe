import { MailOutlineIcon, PhonelinkRingIcon, RestoreIcon } from '@medly-components/icons';
import { TSupportContact } from './types';

export const MIN_FEEDBACK_LENGTH = 50;

const PATH = import.meta.env.MODE === 'production' ? '/app' : '';

export const SUPPORT_CONTACTS: TSupportContact[] = [
    {
        key: 'whatsapp',
        icon: <img src={`${PATH}/whatsapp-icon.svg`} alt="WhatsApp" />,
        label: 'WhatsApp Support:',
        value: '+91-9890546018',
        textVariant: 'h4'
    },
    {
        key: 'phone',
        icon: <PhonelinkRingIcon size="S" iconColor="#555" />,
        label: 'Phone Support:',
        value: '+91-9766798148',
        textVariant: 'h3'
    },
    {
        key: 'email',
        icon: <MailOutlineIcon size="S" iconColor="#555" />,
        label: 'Email Support:',
        value: '<EMAIL>',
        textVariant: 'h4'
    },
    {
        key: 'hours',
        icon: <RestoreIcon size="S" iconColor="#555" />,
        label: 'Support Hours:',
        value: 'Mon - Fri, 11:00 AM - 9:00 PM IST',
        textVariant: 'h4',
        textAlign: 'center'
    }
];

export const FAQ_DATA = [
    {
        categoryTitle: 'Getting Started',
        faqs: [
            {
                question: 'Is SkillWatch really free?',
                answer: 'Yes! SkillWatch is completely free for companies. We plan to introduce paid AI-based features and ready-made KPI templates later — but all core features will remain free.'
            },
            {
                question: 'Who can use SkillWatch?',
                answer: 'SkillWatch is built for everyone — from founders, HRs, and managers to employees and interns. Whether you’re running a 5-member startup or a 5000-person company, you can set up SkillWatch in minutes.'
            },
            {
                question: 'How long does setup take?',
                answer: 'Typically, it takes less than 1 hour to set up your company details, departments, teams, roles, permissions, and employees. The Tutorial Videos guide you through each step smoothly. Setting up KPIs and Review Cycles might take a few more hours, depending on how well you have defined the KPIs and roles/responsibilities within your organisation.'
            },
            {
                question: 'Do I need technical expertise to use SkillWatch?',
                answer: 'Not at all. SkillWatch is designed to be plug-and-play — no IT setup, no training required. Everything is self-explanatory, and tutorial videos are available for all the features.'
            },
            {
                question: 'Can we import employees from our HRMS or Excel file?',
                answer: 'Yes. Employee data can be bulk-imported via CSV upload. Mapping columns like Department, Designation, and Manager ensures seamless data onboarding. Kindly go through the steps mentioned in the Employees module for Bulk Importing the Employees.'
            }
        ]
    },
    {
        categoryTitle: '360-Degree Feedback',
        faqs: [
            {
                question: 'What is 360-degree feedback?',
                answer: '360-degree feedback lets employees receive feedback from peers, managers, and even clients. It provides a holistic view of strengths, areas for improvement, and appreciation patterns.'
            },
            {
                question: 'What types of feedback can I give in SkillWatch?',
                answer: 'You can share three kinds of feedback — Positive Feedback: To highlight what someone is consistently doing well. Improvement Pointers: To help others grow through constructive advice. Appreciation Notes: To recognise efforts, achievements and extraordinary work.'
            },
            {
                question: 'How often should I give feedback?',
                answer: 'We recommend giving feedback regularly — ideally after major tasks, milestones, or client interactions. Continuous feedback leads to faster growth and stronger teams.'
            },
            {
                question: 'When should I give Positive Feedback?',
                answer: 'When to Give: After a task/project where your peer demonstrated a specific behavior worth repeating. During retrospectives, sprint reviews, or right after collaboration moments (not weeks later). When you see them handle a tough situation gracefully — like managing conflict, debugging calmly, or owning mistakes constructively. Purpose: Reinforce good habits before they fade; positive feedback tells people what to keep doing.'
            },
            {
                question: 'When should I give Appreciation Notes?',
                answer: 'When to Give: After a milestone or achievement (release, client delivery, helping hand, hitting targets). When someone goes above and beyond their usual role — helping a teammate, staying late, mentoring, being a role model, etc. During public moments (Slack shoutouts, team meetings, Wall of Fame). Purpose: Build motivation and belonging. Appreciation is emotional — it recognizes effort and intent, not just output.'
            },
            {
                question: 'When should I give Improvement Pointers?',
                answer: 'When to Give: As soon as possible after the behavior or event, so it’s fresh and actionable. Privately, not publicly — and only when your intent is genuinely to help, not criticize. During 1:1s, retros, or peer reviews — ideally framed in context (“I noticed…” not “You always…”). Purpose: Help peers grow without hurting morale. Feedback here should be specific, respectful, and framed as collaboration.'
            },
            {
                question: 'Can I request feedback from others?',
                answer: 'Yes. The Request Feedback feature lets you request feedback for yourself or for another team member. You can even set a context (e.g., “Kindly provide Feedback on the last sprint or presentation or project XYZ”).'
            },
            {
                question: 'Can clients or external stakeholders give feedback?',
                answer: 'Absolutely! You can request feedback from anyone by adding their email address — even if they’re outside your organisation.'
            },
            {
                question: 'Who can see the feedback?',
                answer: 'If you receive feedback directly, only you and your manager can view it. If feedback is given about someone (via a request), both the requester and the subject will be notified. Appreciation feedback will be displayed to the entire organisation, on the Wall of Fame section.'
            },
            {
                question: 'What if I make a mistake while giving feedback?',
                answer: 'You can edit or update feedback until it’s in the draft state. Once submitted, you won’t be able to edit the feedback to ensure data integrity.'
            },
            {
                question: 'Is the feedback linked to the Performance Review?',
                answer: 'Yes. All the feedback given during a particular performance review period gets automatically linked to that review cycle and can be tracked during self-review, manager review, and check-ins. This helps the employee and managers to keep track of all the feedback received throughout the year or review cycle to avoid the recency effect.'
            }
        ]
    },
    {
        categoryTitle: 'Performance Review, KRAs and KPIs',
        faqs: [
            {
                question: 'What is a KRA in SkillWatch?',
                answer: 'KRA stands for Key Responsibility Areas, not Key Result Areas. It focuses on defining responsibilities and outcomes rather than just numeric results — making reviews more meaningful.'
            },
            {
                question: 'What are the main KRA categories?',
                answer: "Every designation's KPIs are grouped under three main KRAs: Knowledge & Skill Growth, Results, Attitude Fitment."
            },
            {
                question: 'Can I assign different weightage to KRAs?',
                answer: 'Yes. You can customise weightage (e.g., Knowledge & Skill Growth 30%, Results 60%,  Attitude 10%) based on their importance in your organisation.'
            },
            {
                question: 'What are KPIs, and how do they work in SkillWatch?',
                answer: 'KPIs (Key Performance Indicators) are measurable tasks or outcomes that you can set based on the SMART framework, under each KRA. Employees can track their progress, and managers can evaluate performance against them.'
            },
            {
                question: 'How do I set a Performance Review Cycle?',
                answer: 'HRs or Admins can define the Review Timeline (e.g., period from Jan – Jun 2026) and set individual timelines for: Self Review, Manager Review, Check-in with Manager.'
            },
            {
                question: 'Can goals be linked to performance reviews?',
                answer: 'Yes. Goals can be aligned with KPIs or KRAs and tracked throughout the performance review period to show measurable outcomes.'
            }
        ]
    },
    {
        categoryTitle: 'Suggestion Box',
        faqs: [
            {
                question: 'What is the Suggestion Box used for?',
                answer: 'It’s a space for employees to share ideas or recommendations for the company, not about individuals. It helps HRs and founders to improve the workplace, culture and processes.'
            },
            {
                question: 'What categories can I select for suggestions?',
                answer: 'Suggestions can be classified under areas like Workplace Culture, Processes & Policies, Productivity & Efficiency, Employee Well-being, Tools & Technology, and Learning & Development.'
            },
            {
                question: 'Will employee get updates on their suggestions?',
                answer: 'Yes. When HR/Admin updates the status (To Do → In Progress → Completed → Deferred), they will get Slack and email notifications.'
            },
            {
                question: 'Can employees see why their suggestion was deferred or rejected?',
                answer: 'Yes. HRs can add comments when updating the status, and those comments are visible to the original submitter via email and Slack notifications.'
            },
            {
                question: 'Can I give anonymous Suggestion?',
                answer: 'Yes. You can give anonymous suggestion if you don’t want the HRs or Founders to know who gave the suggestion.'
            }
        ]
    }
];
