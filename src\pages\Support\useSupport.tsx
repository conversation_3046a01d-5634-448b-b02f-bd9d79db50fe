import { addToast } from '@medly-components/core';
import { useAppSelector } from '@slice';
import { usePostAppFeedbackMutation } from '@slice/services';
import { useCallback, useState } from 'react';
import { MIN_FEEDBACK_LENGTH } from './constants';

export const useSupport = () => {
    const [appFeedback, { isLoading }] = usePostAppFeedbackMutation();
    const [error, setError] = useState('');
    const [feedback, setFeedback] = useState('');
    const userDetails = useAppSelector(state => state.user);

    const handleFeedbackSubmit = async () => {
        try {
            await appFeedback({ feedback, organisationId: userDetails.organisationId }).unwrap();
            addToast({
                variant: 'success',
                header: 'Feedback submitted successfully',
                timer: 3000
            });
            setFeedback('');
        } catch (err) {
            addToast({
                variant: 'error',
                header: 'Something went wrong. Please try again.',
                timer: 3000
            });
        }
    };
    const handleFeedbackChange = useCallback(({ target: { value } }: React.ChangeEvent<HTMLInputElement>) => {
        setFeedback(value);
        if (value.trim().length < MIN_FEEDBACK_LENGTH) {
            setError(`Feedback should be more than ${MIN_FEEDBACK_LENGTH} characters.`);
        } else if (value.trim().length > 500) {
            setError('Feedback should be less than 500 characters.');
        } else {
            setError('');
        }
    }, []);

    return {
        handleFeedbackSubmit,
        isLoading,
        feedback,
        error,
        handleFeedbackChange
    };
};
