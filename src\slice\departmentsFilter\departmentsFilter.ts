import { createSlice } from '@reduxjs/toolkit';

export const initialState = {
    searchText: ''
};

export const departmentsFilterSlice = createSlice({
    name: 'departmentsFilter',
    initialState,
    reducers: {
        updateDepartmentsFilter(state, { payload }) {
            return {
                ...state,
                ...payload
            };
        },
        resetDepartmentsFilter() {
            return initialState;
        }
    }
});
