import { createSlice } from '@reduxjs/toolkit';

export const initialState = {
    search: ''
};

export const designationsFilterSlice = createSlice({
    name: 'designationsFilter',
    initialState,
    reducers: {
        updateDesignationsFilter(state, { payload }) {
            return {
                ...state,
                ...payload
            };
        },
        resetDesignationsFilter() {
            return initialState;
        }
    }
});
