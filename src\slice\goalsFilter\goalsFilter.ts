import { createSlice } from '@reduxjs/toolkit';

// -99 means no filter on backend
export const initialState = {
    goalOwner: [-99],
    goalType: -99,
    progressType: -99,
    activeTab: 'myGoals' // 'myGoals' | 'teamGoals'
};

export const goalsFilterSlice = createSlice({
    name: 'goalsFilter',
    initialState,
    reducers: {
        setGoalsFilter(state, { payload }) {
            return {
                ...state,
                ...payload
            };
        },
        resetGoalsFilter() {
            return initialState;
        }
    }
});
