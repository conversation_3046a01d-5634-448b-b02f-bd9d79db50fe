import { useAppDispatch } from './index';
import { resetTeamReviewFilters } from './reviewTimelineFilter/teamReview';
import { resetDesignationsFilter } from './designationsFilter';
import { resetTeamsFilter } from './teamsFilter';
import { resetRolesFilter } from './rolesFilter';
import { resetDepartmentsFilter } from './departmentsFilter';
import { resetGoalsFilter } from './goalsFilter';
import { resetSuggestionReceivedFilter } from './suggestionReceivedFilter';
import { resetReceivedFeedbackFilter } from './receivedFeedback';
import { resetCheckInFilters } from './checkInWithManager';
import { resetMyFeedbackFilterState } from './myFeedbackFilter';
import { resetSubFeedbackFilter } from './submittedFeedback';
import { resetKPIFilter } from './kpiFilter';
import { dashboardResetReviewCycleFilter } from './dashboardFilter';
import { analyticsResetReviewFilter } from './analyticsFilter';
import { resetFilters as resetFeedbackFilter } from './filter';
import { resetTutorialFilter } from './tutorialFilter';
import { updateReviewCycleFilter } from './reviewCycleFilter';

export const useResetAppFilters = () => {
    const dispatch = useAppDispatch();

    const resetAllFilters = () => {
        dispatch(resetTeamReviewFilters());
        dispatch(resetDesignationsFilter());
        dispatch(resetTeamsFilter());
        dispatch(resetRolesFilter());
        dispatch(resetDepartmentsFilter());
        dispatch(resetGoalsFilter());
        dispatch(resetSuggestionReceivedFilter());
        dispatch(resetReceivedFeedbackFilter());
        dispatch(resetCheckInFilters());
        dispatch(resetMyFeedbackFilterState());
        dispatch(resetSubFeedbackFilter());
        dispatch(resetKPIFilter());
        dispatch(dashboardResetReviewCycleFilter());
        dispatch(analyticsResetReviewFilter());
        dispatch(resetFeedbackFilter());
        dispatch(resetTutorialFilter());
        dispatch(updateReviewCycleFilter({ reviewCycleList: [] }));
    };

    return { resetAllFilters };
};
