import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { TeamReviewState } from './types';

export const initialState: TeamReviewState = {
    employees: [],
    reviewStatus: undefined
};

export const teamReviewSlice = createSlice({
    name: 'teamReviewFilters',
    initialState,
    reducers: {
        updateTeamReviewFilters(state, { payload }: PayloadAction<TeamReviewState>) {
            return {
                ...state,
                ...payload
            };
        },
        resetTeamReviewFilters() {
            return initialState;
        }
    }
});
