import { createSlice } from '@reduxjs/toolkit';

export const initialState = {
    search: ''
};

export const rolesFilterSlice = createSlice({
    name: 'rolesFilter',
    initialState,
    reducers: {
        updateRolesFilter(state, { payload }) {
            return {
                ...state,
                ...payload
            };
        },
        resetRolesFilter() {
            return initialState;
        }
    }
});
