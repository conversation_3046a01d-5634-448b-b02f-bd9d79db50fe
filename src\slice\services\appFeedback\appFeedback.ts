import apiUrls from '@constants/apiUrls';
import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@utils/axiosBaseQuery';
import { createUrlWithParams } from '@utils/createUrlWithParams';

export const appFeedbackApi = createApi({
    reducerPath: 'appFeedbackApi',
    baseQuery: axiosBaseQuery({
        baseUrl: apiUrls.appFeedback
    }),
    tagTypes: ['appFeedback'],

    endpoints: builder => ({
        getAppFeedbacks: builder.query({
            query: payload => ({
                url: createUrlWithParams(payload)
            }),
            providesTags: ['appFeedback']
        }),
        postAppFeedback: builder.mutation({
            query: payload => ({
                url: '/',
                method: 'POST',
                data: payload,
                headers: {
                    'Content-Type': 'application/json; charset=UTF-8'
                }
            }),
            invalidatesTags: ['appFeedback']
        })
    })
});

export const { useGetAppFeedbacksQuery, usePostAppFeedbackMutation } = appFeedbackApi;
