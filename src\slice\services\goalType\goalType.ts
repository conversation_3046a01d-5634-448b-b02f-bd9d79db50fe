import apiUrls from '@constants/apiUrls';
import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@utils/axiosBaseQuery';

export const goalTypesApi = createApi({
    reducerPath: 'goalTypes',
    baseQuery: axiosBaseQuery({
        baseUrl: apiUrls.goalTypes
    }),
    tagTypes: ['goalTypes'],
    endpoints: builder => ({
        getGoalTypes: builder.query({
            query: () => ({
                url: '/'
            }),
            providesTags: ['goalTypes']
        })
    })
});

export const { useGetGoalTypesQuery } = goalTypesApi;
