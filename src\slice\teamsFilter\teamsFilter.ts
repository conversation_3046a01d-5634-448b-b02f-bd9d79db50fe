import { createSlice } from '@reduxjs/toolkit';

export const initialState = {
    search: ''
};

export const teamsFilterSlice = createSlice({
    name: 'teamsFilter',
    initialState,
    reducers: {
        updateTeamsFilter(state, { payload }) {
            return {
                ...state,
                ...payload
            };
        },
        resetTeamsFilter() {
            return initialState;
        }
    }
});
