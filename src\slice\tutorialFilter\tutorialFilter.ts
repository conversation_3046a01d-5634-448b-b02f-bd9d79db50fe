import { createSlice } from '@reduxjs/toolkit';

export const initialState = {
    search: '',
    category: 'all'
};

export const tutorialFilterSlice = createSlice({
    name: 'tutorialFilter',
    initialState,
    reducers: {
        updateCategory(state, { payload }) {
            return {
                ...state,
                category: payload
            };
        },
        updateSearch(state, { payload }) {
            return {
                ...state,
                search: payload
            };
        },
        resetTutorialFilter() {
            return initialState;
        }
    }
});
