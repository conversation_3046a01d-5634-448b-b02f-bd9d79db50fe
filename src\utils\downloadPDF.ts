import { addToast } from '@medly-components/core';
import apiInstance from './api';

export const downloadPDF = async (url: string, fileName: string, onLoadingChange?: (loading: boolean) => void) => {
    try {
        onLoadingChange?.(true);

        const result = await apiInstance.get(url, { responseType: 'blob' });
        if (result.data) {
            const url = window.URL.createObjectURL(new Blob([result.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `${fileName}.pdf`);
            document.body.appendChild(link);
            link.click();
            link.parentNode?.removeChild(link);
            window.URL.revokeObjectURL(url);
        }
    } catch (err) {
        addToast({
            variant: 'error',
            header: 'Something went wrong.',
            timer: 3000
        });
    } finally {
        onLoadingChange?.(false);
    }
};
